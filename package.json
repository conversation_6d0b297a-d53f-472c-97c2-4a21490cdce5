{"type": "module", "name": "nepal-adventure-platform", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --max-warnings 0", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:integration": "vitest integration", "test:seo": "vitest seo", "coverage": "vitest run --coverage", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:deploy": "supabase deploy", "supabase:test": "supabase test db", "supabase:generate-types": "supabase gen types typescript --local > src/types/database.ts"}, "dependencies": {"@google/genai": "^1.9.0", "@hookform/resolvers": "^3.3.2", "@monaco-editor/react": "^4.6.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toolbar": "^1.1.10", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/supabase-js": "^2.38.4", "@tanstack/react-query": "^5.81.5", "@tiptap/extension-document": "^2.25.0", "@tiptap/extension-highlight": "^2.25.0", "@tiptap/extension-image": "^2.25.0", "@tiptap/extension-link": "^2.25.0", "@tiptap/extension-paragraph": "^2.25.0", "@tiptap/extension-placeholder": "^2.25.0", "@tiptap/extension-text": "^2.25.0", "@tiptap/pm": "^2.25.0", "@tiptap/react": "^2.25.0", "@tiptap/starter-kit": "^2.25.0", "@types/hast": "^3.0.4", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "dotenv": "^17.2.0", "hast-util-to-string": "^3.0.1", "highlight.js": "^11.11.1", "lucide-react": "^0.292.0", "next-themes": "^0.4.6", "prosemirror-commands": "^1.7.1", "prosemirror-model": "^1.25.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.40.0", "react": "^18.2.0", "react-day-picker": "^8.9.1", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.48.2", "react-resizable-panels": "^3.0.3", "react-router-dom": "^6.18.0", "recharts": "^3.1.0", "rehype-raw": "^7.0.0", "rehype-stringify": "^10.0.1", "remark-gfm": "^4.0.1", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "shiki": "^3.7.0", "sonner": "^2.0.6", "tailwind-merge": "^2.0.0", "zod": "^3.22.4"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.9.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/testing-library__jest-dom": "^5.14.9", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "@vitejs/plugin-react-swc": "^3.10.2", "@vitest/coverage-v8": "^0.34.6", "@vitest/ui": "^0.34.6", "autoprefixer": "^10.4.16", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jsdom": "^26.1.0", "postcss": "^8.4.31", "prettier": "^3.0.3", "supabase": "^1.110.1", "tailwindcss": "^3.3.5", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.2", "vite": "^4.4.5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^0.34.6"}}