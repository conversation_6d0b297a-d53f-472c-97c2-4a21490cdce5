import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  AlertTriangle, 
  AlertCircle, 
  Info, 
  Bug, 
  Activity, 
  Download,
  Trash2,
  CheckCircle,
  Clock,
  TrendingUp,
  Server,
  Users,
  Zap
} from 'lucide-react';
import { errorService, type ErrorLog } from '@/lib/error-handling/error-service';
import { logger, LogLevel, type LogEntry } from '@/lib/logging/logger';

const ErrorLoggingDashboard = () => {
  const [errors, setErrors] = useState<ErrorLog[]>([]);
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [errorStats, setErrorStats] = useState(errorService.getStats());
  const [logStats, setLogStats] = useState(logger.getStats());

  useEffect(() => {
    // Initial load
    refreshData();

    // Refresh every 30 seconds
    const interval = setInterval(refreshData, 30000);
    return () => clearInterval(interval);
  }, []);

  const refreshData = () => {
    setErrors(errorService.getErrors());
    setLogs(logger.getLogs());
    setErrorStats(errorService.getStats());
    setLogStats(logger.getStats());
  };

  const handleResolveError = (errorId: string) => {
    errorService.resolveError(errorId);
    refreshData();
  };

  const handleClearErrors = () => {
    errorService.clearErrors();
    refreshData();
  };

  const handleClearLogs = () => {
    logger.clearLogs();
    refreshData();
  };

  const handleExportLogs = () => {
    const logsData = logger.exportLogs();
    const blob = new Blob([logsData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `nepal-platform-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getErrorIcon = (level: string) => {
    switch (level) {
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      default:
        return <Info className="w-4 h-4 text-blue-500" />;
    }
  };

  const getLogIcon = (level: LogLevel) => {
    switch (level) {
      case LogLevel.ERROR:
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case LogLevel.WARN:
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case LogLevel.INFO:
        return <Info className="w-4 h-4 text-blue-500" />;
      case LogLevel.DEBUG:
        return <Bug className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Errors</p>
                <p className="text-2xl font-bold text-red-600">{errorStats.errors}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                {errorStats.recent} in last hour
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Error Rate</p>
                <p className="text-2xl font-bold text-orange-600">
                  {errorStats.errorRate.toFixed(1)}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                {errorStats.resolved} resolved
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Logs</p>
                <p className="text-2xl font-bold text-blue-600">{logStats.total}</p>
              </div>
              <Activity className="h-8 w-8 text-blue-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                {logStats.recent} recent
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">System Health</p>
                <p className="text-2xl font-bold text-green-600">
                  {errorStats.errorRate < 5 ? 'Good' : errorStats.errorRate < 15 ? 'Fair' : 'Poor'}
                </p>
              </div>
              <Server className="h-8 w-8 text-green-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                Based on error rate
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="errors" className="space-y-4">
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="errors">
              Errors ({errorStats.errors})
            </TabsTrigger>
            <TabsTrigger value="logs">
              Logs ({logStats.total})
            </TabsTrigger>
            <TabsTrigger value="analytics">
              Analytics
            </TabsTrigger>
          </TabsList>
          
          <div className="flex space-x-2">
            <Button onClick={refreshData} variant="outline" size="sm">
              <Activity className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Button onClick={handleExportLogs} variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        <TabsContent value="errors" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Error Log</CardTitle>
                <CardDescription>
                  Recent errors and exceptions in the system
                </CardDescription>
              </div>
              <Button onClick={handleClearErrors} variant="outline" size="sm">
                <Trash2 className="w-4 h-4 mr-2" />
                Clear All
              </Button>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {errors.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
                      <p>No errors found. System is running smoothly!</p>
                    </div>
                  ) : (
                    errors.map((error) => (
                      <div
                        key={error.id}
                        className={`p-4 border rounded-lg ${
                          error.resolved ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                        }`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3 flex-1">
                            {getErrorIcon(error.level)}
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-1">
                                <Badge variant={error.level === 'error' ? 'destructive' : 'secondary'}>
                                  {error.level}
                                </Badge>
                                {error.context?.component && (
                                  <Badge variant="outline">
                                    {error.context.component}
                                  </Badge>
                                )}
                                {error.resolved && (
                                  <Badge variant="default" className="bg-green-100 text-green-800">
                                    Resolved
                                  </Badge>
                                )}
                              </div>
                              <p className="font-medium text-sm">{error.message}</p>
                              <p className="text-xs text-muted-foreground mt-1">
                                {formatTimestamp(error.timestamp)}
                              </p>
                              {error.context?.action && (
                                <p className="text-xs text-muted-foreground">
                                  Action: {error.context.action}
                                </p>
                              )}
                            </div>
                          </div>
                          {!error.resolved && (
                            <Button
                              onClick={() => handleResolveError(error.id)}
                              size="sm"
                              variant="outline"
                            >
                              <CheckCircle className="w-4 h-4 mr-1" />
                              Resolve
                            </Button>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>System Logs</CardTitle>
                <CardDescription>
                  Detailed system activity and performance logs
                </CardDescription>
              </div>
              <Button onClick={handleClearLogs} variant="outline" size="sm">
                <Trash2 className="w-4 h-4 mr-2" />
                Clear All
              </Button>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-2">
                  {logs.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Activity className="w-12 h-12 mx-auto mb-4" />
                      <p>No logs available</p>
                    </div>
                  ) : (
                    logs.slice(0, 100).map((log, index) => (
                      <div
                        key={index}
                        className="p-3 border rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
                      >
                        <div className="flex items-start space-x-3">
                          {getLogIcon(log.level)}
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <Badge variant="outline" className="text-xs">
                                {LogLevel[log.level]}
                              </Badge>
                              {log.component && (
                                <Badge variant="secondary" className="text-xs">
                                  {log.component}
                                </Badge>
                              )}
                              <span className="text-xs text-muted-foreground">
                                {formatTimestamp(log.timestamp)}
                              </span>
                            </div>
                            <p className="text-sm">{log.message}</p>
                            {log.context && Object.keys(log.context).length > 0 && (
                              <details className="mt-2">
                                <summary className="text-xs text-muted-foreground cursor-pointer">
                                  Context
                                </summary>
                                <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                                  {JSON.stringify(log.context, null, 2)}
                                </pre>
                              </details>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Error Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Errors</span>
                    <span className="text-sm font-medium">{errorStats.errors}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Warnings</span>
                    <span className="text-sm font-medium">{errorStats.warnings}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Resolved</span>
                    <span className="text-sm font-medium">{errorStats.resolved}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Resolution Rate</span>
                    <span className="text-sm font-medium">
                      {errorStats.resolutionRate.toFixed(1)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Log Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Debug</span>
                    <span className="text-sm font-medium">{logStats.debug}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Info</span>
                    <span className="text-sm font-medium">{logStats.info}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Warnings</span>
                    <span className="text-sm font-medium">{logStats.warn}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Errors</span>
                    <span className="text-sm font-medium">{logStats.error}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ErrorLoggingDashboard;
