/**
 * Google Gemini AI Service
 * REST API implementation for reliable browser compatibility
 */

import { errorService } from '@/lib/error-handling/error-service';
import { logger } from '@/lib/logging/logger';

export interface GeminiConfig {
  apiKey: string;
  model?: string;
  temperature?: number;
  topK?: number;
  topP?: number;
  maxOutputTokens?: number;
}

export interface GeminiResponse {
  text: string;
  finishReason?: string;
  safetyRatings?: Array<{
    category: string;
    probability: string;
  }>;
}

export class GeminiService {
  private apiKey: string;
  private model: string;
  private defaultConfig: {
    temperature: number;
    topK: number;
    topP: number;
    maxOutputTokens: number;
  };

  constructor(config: GeminiConfig) {
    console.log('🔧 Initializing GeminiService with config:', {
      hasApiKey: !!config.apiKey,
      model: config.model || 'gemini-2.5-flash'
    });

    if (!config.apiKey) {
      throw new Error('Gemini API key is required');
    }

    this.apiKey = config.apiKey;
    this.model = config.model || 'gemini-2.5-flash';
    this.defaultConfig = {
      temperature: config.temperature || 0.3,
      topK: config.topK || 20,
      topP: config.topP || 0.8,
      maxOutputTokens: config.maxOutputTokens || 1024
    };

    console.log('✅ GeminiService initialized successfully with model:', this.model, 'using REST API');
  }

  async generateContent(
    prompt: string,
    customConfig?: Partial<GeminiConfig>
  ): Promise<GeminiResponse> {
    const startTime = Date.now();
    logger.info('Starting Gemini API content generation', {
      promptLength: prompt.length,
      model: this.model,
      customConfig
    }, 'GeminiService');

    // Validate API key
    if (!this.apiKey || this.apiKey.trim() === '') {
      const error = new Error('Gemini API key is not configured or is empty');
      errorService.logGeminiError(error, 'generateContent');
      throw error;
    }

    // Validate prompt
    if (!prompt || prompt.trim() === '') {
      const error = new Error('Prompt cannot be empty');
      errorService.logGeminiError(error, 'generateContent');
      throw error;
    }

    const maxRetries = 3;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 Attempt ${attempt}/${maxRetries} for Gemini API call`);

        const response = await fetch(
          `https://generativelanguage.googleapis.com/v1beta/models/${this.model}:generateContent?key=${this.apiKey}`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              contents: [{
                parts: [{ text: prompt }]
              }],
              generationConfig: {
                temperature: customConfig?.temperature || this.defaultConfig.temperature,
                topK: customConfig?.topK || this.defaultConfig.topK,
                topP: customConfig?.topP || this.defaultConfig.topP,
                maxOutputTokens: customConfig?.maxOutputTokens || this.defaultConfig.maxOutputTokens,
              },
              safetySettings: [
                {
                  category: 'HARM_CATEGORY_HARASSMENT',
                  threshold: 'BLOCK_MEDIUM_AND_ABOVE'
                },
                {
                  category: 'HARM_CATEGORY_HATE_SPEECH',
                  threshold: 'BLOCK_MEDIUM_AND_ABOVE'
                },
                {
                  category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                  threshold: 'BLOCK_MEDIUM_AND_ABOVE'
                },
                {
                  category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
                  threshold: 'BLOCK_MEDIUM_AND_ABOVE'
                }
              ]
            })
          }
        );

        if (!response.ok) {
          const errorText = await response.text();
          const error = new Error(`Gemini API error (${response.status}): ${errorText}`);

          // Don't retry on certain errors
          if (response.status === 400 || response.status === 401 || response.status === 403) {
            throw error;
          }

          lastError = error;
          if (attempt < maxRetries) {
            const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
            console.log(`⏳ Retrying in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
          throw error;
        }

        const data = await response.json();
        console.log('📥 Gemini API response received:', {
          hasCandidates: !!data.candidates,
          candidatesLength: data.candidates?.length || 0
        });

        if (!data.candidates || data.candidates.length === 0) {
          const error = new Error('No candidates returned from Gemini API');
          lastError = error;
          if (attempt < maxRetries) {
            const delay = Math.pow(2, attempt) * 1000;
            console.log(`⏳ No candidates, retrying in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
          throw error;
        }

        const candidate = data.candidates[0];
        if (candidate.finishReason === 'SAFETY') {
          throw new Error('Content was blocked by Gemini safety filters.');
        }

        const text = candidate.content?.parts?.[0]?.text;
        if (!text || text.trim() === '') {
          const error = new Error('No text content received from Gemini API');
          lastError = error;
          if (attempt < maxRetries) {
            const delay = Math.pow(2, attempt) * 1000;
            console.log(`⏳ Empty response, retrying in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
          throw error;
        }

        const duration = Date.now() - startTime;
        logger.info('Gemini API call successful', {
          duration,
          attempt,
          finishReason: candidate.finishReason,
          responseLength: text.length
        }, 'GeminiService');

        logger.logPerformance('gemini_api_call', duration, {
          model: this.model,
          attempt,
          success: true
        });

        return {
          text,
          finishReason: candidate.finishReason,
          safetyRatings: candidate.safetyRatings?.map((rating: { category: string; probability: string }) => ({
            category: rating.category,
            probability: rating.probability
          }))
        };
      } catch (error) {
        lastError = error as Error;
        const duration = Date.now() - startTime;

        errorService.logGeminiError(error, 'generateContent');
        logger.error('Gemini API call failed', {
          duration,
          attempt,
          error: error instanceof Error ? error.message : String(error)
        }, 'GeminiService');

        // Don't retry on certain errors
        if (error instanceof Error) {
          if (error.message.includes('API_KEY_INVALID') ||
              error.message.includes('safety filters') ||
              error.message.includes('empty')) {
            throw error;
          }
        }

        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1000;
          logger.warn(`Retrying Gemini API call in ${delay}ms`, {
            attempt,
            maxRetries,
            delay
          }, 'GeminiService');
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
      }
    }

    // If we get here, all retries failed
    const finalError = lastError || new Error('All retry attempts failed');
    const totalDuration = Date.now() - startTime;

    errorService.logGeminiError(finalError, 'generateContent');
    logger.error('All Gemini API retry attempts failed', {
      totalDuration,
      maxRetries,
      finalError: finalError.message
    }, 'GeminiService');

    throw finalError;
  }

  async generateSEOContent(
    content: string,
    title: string,
    excerpt?: string,
    tags?: string[],
    contentType: 'blog' | 'trek' | 'page' = 'blog'
  ): Promise<{
    optimizedTitle: string;
    optimizedExcerpt: string;
    optimizedContent: string;
    optimizedTags: string[];
  }> {
    const prompt = this.buildSEOOptimizationPrompt(content, title, excerpt, tags, contentType);

    const response = await this.generateContent(prompt, {
      temperature: 0.3, // Lower temperature for more consistent SEO output
      maxOutputTokens: 2048
    });

    return this.parseSEOResponse(response.text);
  }

  private buildSEOOptimizationPrompt(
    content: string,
    title: string,
    excerpt?: string,
    tags?: string[],
    contentType: 'blog' | 'trek' | 'page' = 'blog'
  ): string {
    const contentTypeContext = {
      blog: 'travel blog post about Nepal trekking and adventures',
      trek: 'trekking package description for Nepal adventures',
      page: 'informational page about Nepal travel and trekking'
    };

    return `You are an expert SEO content optimizer specializing in Nepal adventure tourism, trekking, and travel content.

CONTEXT: This is a ${contentTypeContext[contentType]} targeting international tourists from USA, UK, Australia, and Europe who are interested in adventurous travel, digital nomads, and eco-conscious tourism.

CURRENT CONTENT:
Title: ${title}
${excerpt ? `Excerpt: ${excerpt}` : ''}
${tags && tags.length > 0 ? `Current Tags: ${tags.join(', ')}` : ''}

Content:
${content}

OPTIMIZATION REQUIREMENTS:
1. Optimize for search engines while maintaining readability and authenticity
2. Target keywords: Nepal trekking, Himalayan adventures, eco-tourism, adventure travel
3. Include location-specific terms: Everest, Annapurna, Kathmandu, Pokhara
4. Appeal to adventure seekers, digital nomads, and eco-conscious travelers
5. Maintain the authentic voice and cultural sensitivity
6. Ensure content is engaging and informative

IMPORTANT: Return ONLY a valid JSON object with this exact structure:
{
  "optimizedTitle": "SEO-optimized title (max 60 characters)",
  "optimizedExcerpt": "SEO-optimized excerpt/meta description (max 160 characters)",
  "optimizedContent": "SEO-optimized content maintaining original structure and authenticity",
  "optimizedTags": ["tag1", "tag2", "tag3", "tag4", "tag5"]
}

Do not include any other text, explanations, or formatting outside the JSON object.`;
  }

  private parseSEOResponse(responseText: string): {
    optimizedTitle: string;
    optimizedExcerpt: string;
    optimizedContent: string;
    optimizedTags: string[];
  } {
    try {
      // Extract JSON from response
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in Gemini response');
      }

      const parsed = JSON.parse(jsonMatch[0]);

      // Validate required fields
      if (!parsed.optimizedTitle || !parsed.optimizedExcerpt || !parsed.optimizedContent || !Array.isArray(parsed.optimizedTags)) {
        throw new Error('Invalid response format from Gemini API');
      }

      return {
        optimizedTitle: parsed.optimizedTitle,
        optimizedExcerpt: parsed.optimizedExcerpt,
        optimizedContent: parsed.optimizedContent,
        optimizedTags: parsed.optimizedTags
      };
    } catch (error) {
      throw new Error(`Failed to parse Gemini response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Health check method
  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; details: string }> {
    try {
      const response = await this.generateContent(
        'Respond with "OK" if you can process this request.',
        { maxOutputTokens: 10 }
      );

      if (response.text.includes('OK')) {
        return { status: 'healthy', details: 'Gemini API is responding correctly' };
      } else {
        return { status: 'unhealthy', details: 'Unexpected response from Gemini API' };
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        details: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

// Factory function for easy instantiation
export function createGeminiService(apiKey?: string): GeminiService {
  const key = apiKey || import.meta.env.VITE_GEMINI_API_KEY;

  if (!key) {
    throw new Error('Gemini API key not found. Please set VITE_GEMINI_API_KEY environment variable.');
  }

  return new GeminiService({
    apiKey: key,
    model: 'gemini-2.5-flash',
    temperature: 0.3,
    topK: 20,
    topP: 0.8,
    maxOutputTokens: 1024
  });
}
