import { toast } from '@/hooks/use-toast';

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

export interface ErrorLog {
  id: string;
  timestamp: Date;
  level: 'error' | 'warning' | 'info';
  message: string;
  stack?: string;
  context?: ErrorContext;
  userAgent: string;
  url: string;
  resolved: boolean;
}

class ErrorService {
  private errors: ErrorLog[] = [];
  private maxErrors = 100; // Keep last 100 errors in memory

  /**
   * Log an error with context
   */
  logError(error: Error | string, context?: ErrorContext, level: 'error' | 'warning' | 'info' = 'error') {
    const errorLog: ErrorLog = {
      id: this.generateId(),
      timestamp: new Date(),
      level,
      message: typeof error === 'string' ? error : error.message,
      stack: typeof error === 'object' ? error.stack : undefined,
      context,
      userAgent: navigator.userAgent,
      url: window.location.href,
      resolved: false,
    };

    // Add to in-memory store
    this.errors.unshift(errorLog);
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error logged:', errorLog);
    }

    // Send to external service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToExternalService(errorLog);
    }

    // Show user-friendly toast for critical errors
    if (level === 'error' && this.shouldShowToUser(errorLog)) {
      this.showUserNotification(errorLog);
    }

    return errorLog.id;
  }

  /**
   * Log API errors with specific handling
   */
  logApiError(error: any, endpoint: string, method: string = 'GET') {
    const context: ErrorContext = {
      component: 'API',
      action: `${method} ${endpoint}`,
      metadata: {
        status: error.status,
        statusText: error.statusText,
        endpoint,
        method,
      },
    };

    return this.logError(error, context);
  }

  /**
   * Log Supabase errors
   */
  logSupabaseError(error: any, table?: string, operation?: string) {
    const context: ErrorContext = {
      component: 'Supabase',
      action: operation || 'database_operation',
      metadata: {
        table,
        code: error.code,
        details: error.details,
        hint: error.hint,
      },
    };

    return this.logError(error, context);
  }

  /**
   * Log Gemini API errors
   */
  logGeminiError(error: any, operation: string) {
    const context: ErrorContext = {
      component: 'GeminiAPI',
      action: operation,
      metadata: {
        type: error.name,
        details: error.details,
      },
    };

    return this.logError(error, context);
  }

  /**
   * Get all errors
   */
  getErrors(): ErrorLog[] {
    return [...this.errors];
  }

  /**
   * Get errors by level
   */
  getErrorsByLevel(level: 'error' | 'warning' | 'info'): ErrorLog[] {
    return this.errors.filter(error => error.level === level);
  }

  /**
   * Get recent errors (last hour)
   */
  getRecentErrors(): ErrorLog[] {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    return this.errors.filter(error => error.timestamp > oneHourAgo);
  }

  /**
   * Mark error as resolved
   */
  resolveError(errorId: string) {
    const error = this.errors.find(e => e.id === errorId);
    if (error) {
      error.resolved = true;
    }
  }

  /**
   * Clear all errors
   */
  clearErrors() {
    this.errors = [];
  }

  /**
   * Get error statistics
   */
  getStats() {
    const total = this.errors.length;
    const errors = this.errors.filter(e => e.level === 'error').length;
    const warnings = this.errors.filter(e => e.level === 'warning').length;
    const resolved = this.errors.filter(e => e.resolved).length;
    const recent = this.getRecentErrors().length;

    return {
      total,
      errors,
      warnings,
      resolved,
      recent,
      errorRate: total > 0 ? (errors / total) * 100 : 0,
      resolutionRate: total > 0 ? (resolved / total) * 100 : 0,
    };
  }

  private generateId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private shouldShowToUser(errorLog: ErrorLog): boolean {
    // Don't show network errors or API rate limits to users
    const silentErrors = [
      'network error',
      'rate limit',
      'quota exceeded',
      'timeout',
    ];

    return !silentErrors.some(silent => 
      errorLog.message.toLowerCase().includes(silent)
    );
  }

  private showUserNotification(errorLog: ErrorLog) {
    const userFriendlyMessage = this.getUserFriendlyMessage(errorLog);
    
    toast({
      title: "Something went wrong",
      description: userFriendlyMessage,
      variant: "destructive",
    });
  }

  private getUserFriendlyMessage(errorLog: ErrorLog): string {
    // Map technical errors to user-friendly messages
    const messageMap: Record<string, string> = {
      'network error': 'Please check your internet connection and try again.',
      'rate limit': 'Too many requests. Please wait a moment and try again.',
      'quota exceeded': 'Service temporarily unavailable. Please try again later.',
      'timeout': 'Request timed out. Please try again.',
      'unauthorized': 'Please log in again to continue.',
      'forbidden': 'You don\'t have permission to perform this action.',
      'not found': 'The requested resource was not found.',
      'server error': 'Server error. Our team has been notified.',
    };

    const message = errorLog.message.toLowerCase();
    for (const [key, friendlyMessage] of Object.entries(messageMap)) {
      if (message.includes(key)) {
        return friendlyMessage;
      }
    }

    return 'An unexpected error occurred. Please try again.';
  }

  private async sendToExternalService(errorLog: ErrorLog) {
    try {
      // In production, send to error tracking service
      // Example: Sentry, LogRocket, Bugsnag, etc.
      
      // For now, we'll just store in localStorage as a fallback
      const storedErrors = localStorage.getItem('nepal_platform_errors');
      const errors = storedErrors ? JSON.parse(storedErrors) : [];
      errors.unshift(errorLog);
      
      // Keep only last 50 errors in localStorage
      if (errors.length > 50) {
        errors.splice(50);
      }
      
      localStorage.setItem('nepal_platform_errors', JSON.stringify(errors));
    } catch (error) {
      console.error('Failed to send error to external service:', error);
    }
  }
}

// Global error service instance
export const errorService = new ErrorService();

// Global error handler for unhandled errors
window.addEventListener('error', (event) => {
  errorService.logError(event.error || event.message, {
    component: 'Global',
    action: 'unhandled_error',
    metadata: {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
    },
  });
});

// Global handler for unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  errorService.logError(event.reason, {
    component: 'Global',
    action: 'unhandled_promise_rejection',
  });
});

export default errorService;
