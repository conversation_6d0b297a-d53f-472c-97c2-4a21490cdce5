export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  message: string;
  context?: Record<string, any>;
  component?: string;
  userId?: string;
  sessionId?: string;
}

class Logger {
  private logs: LogEntry[] = [];
  private maxLogs = 1000;
  private sessionId: string;
  private currentLogLevel: LogLevel;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.currentLogLevel = process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG;
  }

  /**
   * Log debug message (development only)
   */
  debug(message: string, context?: Record<string, any>, component?: string) {
    this.log(LogLevel.DEBUG, message, context, component);
  }

  /**
   * Log info message
   */
  info(message: string, context?: Record<string, any>, component?: string) {
    this.log(LogLevel.INFO, message, context, component);
  }

  /**
   * Log warning message
   */
  warn(message: string, context?: Record<string, any>, component?: string) {
    this.log(LogLevel.WARN, message, context, component);
  }

  /**
   * Log error message
   */
  error(message: string, context?: Record<string, any>, component?: string) {
    this.log(LogLevel.ERROR, message, context, component);
  }

  /**
   * Log API calls
   */
  logApiCall(method: string, endpoint: string, status: number, duration: number, context?: Record<string, any>) {
    const level = status >= 400 ? LogLevel.ERROR : status >= 300 ? LogLevel.WARN : LogLevel.INFO;
    this.log(level, `API ${method} ${endpoint} - ${status}`, {
      ...context,
      method,
      endpoint,
      status,
      duration,
      type: 'api_call',
    }, 'API');
  }

  /**
   * Log user actions
   */
  logUserAction(action: string, context?: Record<string, any>, userId?: string) {
    this.log(LogLevel.INFO, `User action: ${action}`, {
      ...context,
      type: 'user_action',
    }, 'UserAction', userId);
  }

  /**
   * Log performance metrics
   */
  logPerformance(metric: string, value: number, context?: Record<string, any>) {
    this.log(LogLevel.INFO, `Performance: ${metric} = ${value}ms`, {
      ...context,
      metric,
      value,
      type: 'performance',
    }, 'Performance');
  }

  /**
   * Log SEO optimization events
   */
  logSEOEvent(event: string, context?: Record<string, any>) {
    this.log(LogLevel.INFO, `SEO: ${event}`, {
      ...context,
      type: 'seo_event',
    }, 'SEO');
  }

  /**
   * Log content generation events
   */
  logContentEvent(event: string, context?: Record<string, any>) {
    this.log(LogLevel.INFO, `Content: ${event}`, {
      ...context,
      type: 'content_event',
    }, 'Content');
  }

  /**
   * Get all logs
   */
  getLogs(): LogEntry[] {
    return [...this.logs];
  }

  /**
   * Get logs by level
   */
  getLogsByLevel(level: LogLevel): LogEntry[] {
    return this.logs.filter(log => log.level === level);
  }

  /**
   * Get logs by component
   */
  getLogsByComponent(component: string): LogEntry[] {
    return this.logs.filter(log => log.component === component);
  }

  /**
   * Get recent logs (last hour)
   */
  getRecentLogs(): LogEntry[] {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    return this.logs.filter(log => log.timestamp > oneHourAgo);
  }

  /**
   * Get log statistics
   */
  getStats() {
    const total = this.logs.length;
    const debug = this.logs.filter(l => l.level === LogLevel.DEBUG).length;
    const info = this.logs.filter(l => l.level === LogLevel.INFO).length;
    const warn = this.logs.filter(l => l.level === LogLevel.WARN).length;
    const error = this.logs.filter(l => l.level === LogLevel.ERROR).length;
    const recent = this.getRecentLogs().length;

    return {
      total,
      debug,
      info,
      warn,
      error,
      recent,
      errorRate: total > 0 ? (error / total) * 100 : 0,
    };
  }

  /**
   * Clear all logs
   */
  clearLogs() {
    this.logs = [];
  }

  /**
   * Export logs for analysis
   */
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  /**
   * Set log level
   */
  setLogLevel(level: LogLevel) {
    this.currentLogLevel = level;
  }

  private log(level: LogLevel, message: string, context?: Record<string, any>, component?: string, userId?: string) {
    // Skip if below current log level
    if (level < this.currentLogLevel) {
      return;
    }

    const logEntry: LogEntry = {
      timestamp: new Date(),
      level,
      message,
      context,
      component,
      userId,
      sessionId: this.sessionId,
    };

    // Add to in-memory store
    this.logs.unshift(logEntry);
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }

    // Console output with appropriate method
    this.outputToConsole(logEntry);

    // Send to external service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToExternalService(logEntry);
    }
  }

  private outputToConsole(logEntry: LogEntry) {
    const timestamp = logEntry.timestamp.toISOString();
    const component = logEntry.component ? `[${logEntry.component}]` : '';
    const logMessage = `${timestamp} ${component} ${logEntry.message}`;

    switch (logEntry.level) {
      case LogLevel.DEBUG:
        console.debug(logMessage, logEntry.context);
        break;
      case LogLevel.INFO:
        console.info(logMessage, logEntry.context);
        break;
      case LogLevel.WARN:
        console.warn(logMessage, logEntry.context);
        break;
      case LogLevel.ERROR:
        console.error(logMessage, logEntry.context);
        break;
    }
  }

  private async sendToExternalService(logEntry: LogEntry) {
    try {
      // In production, send to logging service
      // Example: LogRocket, DataDog, Splunk, etc.
      
      // For now, we'll store in localStorage as a fallback
      const storedLogs = localStorage.getItem('nepal_platform_logs');
      const logs = storedLogs ? JSON.parse(storedLogs) : [];
      logs.unshift(logEntry);
      
      // Keep only last 100 logs in localStorage
      if (logs.length > 100) {
        logs.splice(100);
      }
      
      localStorage.setItem('nepal_platform_logs', JSON.stringify(logs));
    } catch (error) {
      console.error('Failed to send log to external service:', error);
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Global logger instance
export const logger = new Logger();

// Helper functions for common logging patterns
export const logApiCall = (method: string, endpoint: string, status: number, duration: number, context?: Record<string, any>) => {
  logger.logApiCall(method, endpoint, status, duration, context);
};

export const logUserAction = (action: string, context?: Record<string, any>, userId?: string) => {
  logger.logUserAction(action, context, userId);
};

export const logPerformance = (metric: string, value: number, context?: Record<string, any>) => {
  logger.logPerformance(metric, value, context);
};

export const logSEOEvent = (event: string, context?: Record<string, any>) => {
  logger.logSEOEvent(event, context);
};

export const logContentEvent = (event: string, context?: Record<string, any>) => {
  logger.logContentEvent(event, context);
};

export default logger;
